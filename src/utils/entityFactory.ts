import UUID from './uuid'
import type { Song, Set, SetList, Show, Venue, Artist, Act } from '@/types'

const createBaseEntity = () => ({
  id: UUID(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
})

export const createSong = (data: Partial<Song> = {}): Song => ({
  ...createBaseEntity(),
  title: '',
  key: '',
  durationSecs: 0,
  bpm: 0,
  ...data
})

export const createSet = (data: Partial<Set> = {}): Set => ({
  ...createBaseEntity(),
  name: '',
  songs: [],
  ...data
})

export const createSetList = (data: Partial<SetList> = {}): SetList => ({
  ...createBaseEntity(),
  name: '',
  sets: [],
  hasEncore: false,
  ...data
})

export const createShow = (data: Partial<Show> = {}): Show => ({
  ...createBaseEntity(),
  title: '',
  venue: '',
  act: '',
  date: new Date().toISOString().split('T')[0],
  sets: [],
  storageLocation: 'local',
  ...data
})

export const createVenue = (data: Partial<Venue> = {}): Venue => ({
  ...createBaseEntity(),
  name: '',
  ...data
})

export const createArtist = (data: Partial<Artist> = {}): Artist => ({
  ...createBaseEntity(),
  name: '',
  ...data
})

export const createAct = (data: Partial<Act> = {}): Act => ({
  ...createBaseEntity(),
  name: '',
  artists: [],
  ...data
})
