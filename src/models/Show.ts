import { ref, computed } from 'vue'
import type { Show as ShowType, StorageLocation, Set as SetType } from '@/types'
import UUID from '@/utils/uuid'
import { Set } from './Set'

export class Show {
  private _id: string
  private _title = ref('')
  private _venue = ref('')
  private _act = ref('')
  private _date = ref('')
  private _sets = ref<Set[]>([])
  private _notes = ref('')
  private _storageLocation: StorageLocation
  private _createdAt: string
  private _updatedAt: string

  constructor(data?: Partial<ShowType>) {
    this._id = data?.id || UUID()
    this._title.value = data?.title || ''
    this._venue.value = data?.venue || ''
    this._act.value = data?.act || ''
    this._date.value = data?.date || new Date().toISOString().split('T')[0]
    this._sets.value = (data?.sets || []).map(set => new Set(set))
    this._notes.value = data?.notes || ''
    this._storageLocation = data?.storageLocation || 'local'
    this._createdAt = data?.createdAt || new Date().toISOString()
    this._updatedAt = data?.updatedAt || new Date().toISOString()

    // Ensure at least one set exists
    if (this._sets.value.length === 0) {
      this.addSet()
    }
  }

  // Getters
  get id(): string {
    return this._id
  }
  get title(): string {
    return this._title.value
  }
  get venue(): string {
    return this._venue.value
  }
  get act(): string {
    return this._act.value
  }
  get date(): string {
    return this._date.value
  }
  get sets(): Set[] {
    return this._sets.value
  }
  get notes(): string {
    return this._notes.value
  }
  get storageLocation(): StorageLocation {
    return this._storageLocation
  }
  get createdAt(): string {
    return this._createdAt
  }
  get updatedAt(): string {
    return this._updatedAt
  }

  // Computed properties
  get totalDuration(): number {
    return this._sets.value.reduce((total, set) => total + set.duration, 0)
  }

  get totalSongs(): number {
    return this._sets.value.reduce((total, set) => total + set.songCount, 0)
  }

  get formattedDate(): string {
    const date = new Date(this._date.value)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Setters with automatic timestamp updates
  set title(value: string) {
    this._title.value = value
    this.touch()
  }

  set venue(value: string) {
    this._venue.value = value
    this.touch()
  }

  set act(value: string) {
    this._act.value = value
    this.touch()
  }

  set date(value: string) {
    this._date.value = value
    this.touch()
  }

  set notes(value: string) {
    this._notes.value = value
    this.touch()
  }

  // Methods
  addSet(): void {
    if (this._sets.value.length < 4) {
      this._sets.value.push(new Set())
      this.touch()
    }
  }

  removeSet(index: number): void {
    if (
      index >= 0 &&
      index < this._sets.value.length &&
      this._sets.value.length > 1
    ) {
      this._sets.value.splice(index, 1)
      this.touch()
    }
  }

  updateSetTitle(setIndex: number, title: string): void {
    if (setIndex >= 0 && setIndex < this._sets.value.length) {
      this._sets.value[setIndex].title = title
      this.touch()
    }
  }

  moveSet(fromIndex: number, toIndex: number): void {
    const set = this._sets.value.splice(fromIndex, 1)[0]
    this._sets.value.splice(toIndex, 0, set)
    this.touch()
  }

  private touch(): void {
    this._updatedAt = new Date().toISOString()
  }

  // Convert to plain object for storage/serialization
  toJSON(): ShowType {
    return {
      id: this._id,
      title: this._title.value,
      venue: this._venue.value,
      act: this._act.value,
      date: this._date.value,
      sets: this._sets.value.map(set => set.toJSON()),
      notes: this._notes.value,
      storageLocation: this._storageLocation,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    }
  }

  // Create a Show instance from a plain object
  static fromJSON(data: ShowType): Show {
    return new Show(data)
  }
}
